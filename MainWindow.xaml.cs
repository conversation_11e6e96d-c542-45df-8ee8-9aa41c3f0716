﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using Microsoft.Win32;
using DataCapture_Benchmark.Models;
using DataCapture_Benchmark.Services;

namespace DataCapture_Benchmark
{
    /// <summary>
    /// Interaction logic for MainWindow.xaml
    /// </summary>
    public partial class MainWindow : Window
    {
        private readonly ProjectService _projectService;
        private readonly ExcelService _excelService;
        private readonly ConfigurationService _configurationService;
        private readonly LoggingService _loggingService;
        private DatabaseService? _databaseService;
        private ProjectInfo? _selectedProject;
        private List<ExcelRowData>? _excelData;

        public MainWindow()
        {
            InitializeComponent();
            _projectService = new ProjectService();
            _excelService = new ExcelService();
            _configurationService = new ConfigurationService();
            _loggingService = new LoggingService();

            InitializeApplication();
        }

        private async void InitializeApplication()
        {
            try
            {
                _loggingService.LogInfo("Application starting");

                // Load configuration
                var config = _configurationService.GetConfiguration();

                // Set default connection string if available
                if (!string.IsNullOrEmpty(config.ConnectionStrings.DefaultConnection))
                {
                    ConnectionStringTextBox.Text = config.ConnectionStrings.DefaultConnection;
                }

                await LoadProjects();

                _loggingService.LogInfo("Application initialized successfully");
            }
            catch (Exception ex)
            {
                _loggingService.LogError("Error during application initialization", ex);
                UpdateStatus($"Initialization error: {ex.Message}");
            }
        }

        private async Task LoadProjects()
        {
            try
            {
                UpdateStatus("Loading projects...");
                await Task.Run(() => _projectService.LoadProjects());
                UpdateStatus("Projects loaded successfully");
                _loggingService.LogInfo("Projects loaded successfully");
            }
            catch (Exception ex)
            {
                var errorMessage = $"Error loading projects: {ex.Message}";
                UpdateStatus(errorMessage);
                _loggingService.LogError("Error loading projects", ex);
                MessageBox.Show(errorMessage, "Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void UpdateStatus(string message)
        {
            StatusTextBlock.Text = $"{DateTime.Now:HH:mm:ss} - {message}";
        }

        #region Generate Excel Tab

        private void SearchTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            var searchTerm = SearchTextBox.Text;

            if (string.IsNullOrWhiteSpace(searchTerm))
            {
                ProjectListBox.ItemsSource = null;
                return;
            }

            var results = _projectService.SearchProjects(searchTerm);
            ProjectListBox.ItemsSource = results;
            ProjectListBox.DisplayMemberPath = "DisplayText";
        }

        private void ProjectListBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            _selectedProject = ProjectListBox.SelectedItem as ProjectInfo;

            if (_selectedProject != null)
            {
                SelectedProjectName.Text = _selectedProject.Name;
                SelectedProjectDetails.Text = $"Building: {_selectedProject.Building}\n" +
                                            $"Client: {_selectedProject.Client}\n" +
                                            $"Job Director: {_selectedProject.JobDirector}\n" +
                                            $"Project Number: {_selectedProject.ProjectNumber}";

                SelectedProjectPanel.Visibility = Visibility.Visible;
                GenerateButton.IsEnabled = true;
            }
            else
            {
                SelectedProjectPanel.Visibility = Visibility.Collapsed;
                GenerateButton.IsEnabled = false;
            }
        }

        private async void GenerateButton_Click(object sender, RoutedEventArgs e)
        {
            if (_selectedProject == null) return;

            try
            {
                GenerateButton.IsEnabled = false;
                UpdateStatus("Generating Excel file...");

                var outputPath = await Task.Run(() => _excelService.GenerateExcelFile(_selectedProject));

                UpdateStatus($"Excel file generated: {Path.GetFileName(outputPath)}");

                var result = MessageBox.Show(
                    $"Excel file generated successfully!\n\nPath: {outputPath}\n\nWould you like to open it now?",
                    "Success",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Information);

                if (result == MessageBoxResult.Yes)
                {
                    _excelService.OpenExcelFile(outputPath);
                }
            }
            catch (Exception ex)
            {
                UpdateStatus($"Error generating Excel file: {ex.Message}");
                MessageBox.Show($"Error generating Excel file: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                GenerateButton.IsEnabled = true;
            }
        }

        #endregion

        #region Upload Data Tab

        private void BrowseButton_Click(object sender, RoutedEventArgs e)
        {
            var openFileDialog = new OpenFileDialog
            {
                Filter = "Excel files (*.xlsx;*.xls)|*.xlsx;*.xls|All files (*.*)|*.*",
                Title = "Select Excel file to upload"
            };

            if (openFileDialog.ShowDialog() == true)
            {
                FilePathTextBox.Text = openFileDialog.FileName;
                LoadAndValidateExcelFile(openFileDialog.FileName);
            }
        }

        private async void LoadAndValidateExcelFile(string filePath)
        {
            try
            {
                UpdateStatus("Loading Excel file...");

                _excelData = await Task.Run(() => _excelService.ReadExcelFile(filePath));

                if (_excelData.Count == 0)
                {
                    ValidationResultsTextBlock.Text = "No data found in Excel file. Please check the file format and ensure it contains the required columns: Source, Discipline, Attribute, Value, Confidence.";
                    UploadButton.IsEnabled = false;
                    UpdateStatus("No data found in Excel file");
                    return;
                }

                if (_databaseService != null)
                {
                    var validationErrors = _databaseService.ValidateData(_excelData);

                    if (validationErrors.Any())
                    {
                        ValidationResultsTextBlock.Text = "Validation Errors:\n" + string.Join("\n", validationErrors);
                        UploadButton.IsEnabled = false;
                        UpdateStatus($"Validation failed: {validationErrors.Count} errors found");
                    }
                    else
                    {
                        ValidationResultsTextBlock.Text = $"✓ Validation successful! {_excelData.Count} rows ready for upload.\n\nData preview:\n" +
                            GetDataPreview(_excelData);
                        UploadButton.IsEnabled = !string.IsNullOrWhiteSpace(ConnectionStringTextBox.Text);
                        UpdateStatus($"Excel file loaded and validated: {_excelData.Count} rows");
                    }
                }
                else
                {
                    ValidationResultsTextBlock.Text = $"Excel file loaded: {_excelData.Count} rows.\n\nData preview:\n" +
                        GetDataPreview(_excelData) + "\n\nPlease test database connection to validate data.";
                    UpdateStatus($"Excel file loaded: {_excelData.Count} rows");
                }
            }
            catch (Exception ex)
            {
                ValidationResultsTextBlock.Text = $"Error loading Excel file: {ex.Message}";
                UpdateStatus($"Error loading Excel file: {ex.Message}");
                UploadButton.IsEnabled = false;
            }
        }

        private string GetDataPreview(List<ExcelRowData> data)
        {
            if (data == null || data.Count == 0)
                return "No data available";

            var preview = new System.Text.StringBuilder();
            var previewCount = Math.Min(5, data.Count);

            for (int i = 0; i < previewCount; i++)
            {
                var row = data[i];
                preview.AppendLine($"Row {i + 1}: {row.Source} | {row.Discipline} | {row.Attribute} | {row.Value} | {row.Confidence}");
            }

            if (data.Count > previewCount)
            {
                preview.AppendLine($"... and {data.Count - previewCount} more rows");
            }

            return preview.ToString();
        }

        private async void TestConnectionButton_Click(object sender, RoutedEventArgs e)
        {
            var connectionString = ConnectionStringTextBox.Text;

            if (string.IsNullOrWhiteSpace(connectionString))
            {
                MessageBox.Show("Please enter a connection string", "Error", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            try
            {
                TestConnectionButton.IsEnabled = false;
                UpdateStatus("Testing database connection...");

                _databaseService = new DatabaseService(connectionString);
                var isConnected = await _databaseService.TestConnectionAsync();

                if (isConnected)
                {
                    UpdateStatus("Database connection successful");
                    MessageBox.Show("Database connection successful!", "Success", MessageBoxButton.OK, MessageBoxImage.Information);

                    // Re-validate Excel data if it's loaded
                    if (_excelData != null && !string.IsNullOrWhiteSpace(FilePathTextBox.Text))
                    {
                        LoadAndValidateExcelFile(FilePathTextBox.Text);
                    }
                }
                else
                {
                    UpdateStatus("Database connection failed");
                    MessageBox.Show("Database connection failed. Please check your connection string.", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            catch (Exception ex)
            {
                UpdateStatus($"Database connection error: {ex.Message}");
                MessageBox.Show($"Database connection error: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                TestConnectionButton.IsEnabled = true;
            }
        }

        private async void UploadButton_Click(object sender, RoutedEventArgs e)
        {
            if (_excelData == null || _databaseService == null)
            {
                MessageBox.Show("Please load an Excel file and test the database connection first.", "Error", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            // Get project info for upload
            var projectSelectionDialog = new ProjectSelectionDialog(_projectService);
            if (projectSelectionDialog.ShowDialog() != true || projectSelectionDialog.SelectedProject == null)
            {
                return;
            }

            var selectedProject = projectSelectionDialog.SelectedProject;

            try
            {
                UploadButton.IsEnabled = false;
                UpdateStatus("Uploading data to database...");

                var filename = Path.GetFileName(FilePathTextBox.Text);
                await _databaseService.UploadDataAsync(_excelData, filename, selectedProject.PUID, selectedProject.ProjectNumber);

                UpdateStatus($"Data uploaded successfully: {_excelData.Count} rows");
                MessageBox.Show($"Data uploaded successfully!\n\n{_excelData.Count} rows uploaded to the database.", "Success", MessageBoxButton.OK, MessageBoxImage.Information);

                // Clear the form
                FilePathTextBox.Text = string.Empty;
                ValidationResultsTextBlock.Text = string.Empty;
                _excelData = null;
            }
            catch (Exception ex)
            {
                UpdateStatus($"Upload error: {ex.Message}");
                MessageBox.Show($"Upload error: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                UploadButton.IsEnabled = true;
            }
        }

        #endregion

        #region Menu Event Handlers

        private void MenuOpenExcel_Click(object sender, RoutedEventArgs e)
        {
            BrowseButton_Click(sender, e);
        }

        private void MenuSaveConfig_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var config = _configurationService.GetConfiguration();
                config.ConnectionStrings.DefaultConnection = ConnectionStringTextBox.Text;
                _configurationService.SaveConfiguration(config);

                UpdateStatus("Configuration saved successfully");
                MessageBox.Show("Configuration saved successfully!", "Success", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                _loggingService.LogError("Error saving configuration", ex);
                MessageBox.Show($"Error saving configuration: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void MenuExit_Click(object sender, RoutedEventArgs e)
        {
            Close();
        }

        private void MenuViewLogs_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var logPath = _loggingService.GetLogFilePath();
                if (File.Exists(logPath))
                {
                    System.Diagnostics.Process.Start(new System.Diagnostics.ProcessStartInfo
                    {
                        FileName = "notepad.exe",
                        Arguments = logPath,
                        UseShellExecute = true
                    });
                }
                else
                {
                    MessageBox.Show("No log file found.", "Information", MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error opening log file: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void MenuClearLogs_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var result = MessageBox.Show("Are you sure you want to clear all logs?", "Confirm", MessageBoxButton.YesNo, MessageBoxImage.Question);
                if (result == MessageBoxResult.Yes)
                {
                    var logPath = _loggingService.GetLogFilePath();
                    if (File.Exists(logPath))
                    {
                        File.WriteAllText(logPath, string.Empty);
                        UpdateStatus("Logs cleared successfully");
                        _loggingService.LogInfo("Logs cleared by user");
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error clearing logs: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void MenuTestConnection_Click(object sender, RoutedEventArgs e)
        {
            TestConnectionButton_Click(sender, e);
        }

        private void MenuAbout_Click(object sender, RoutedEventArgs e)
        {
            var aboutMessage = "Data Capture Benchmark Application\n\n" +
                              "Version: 1.0.0\n" +
                              "Built with WPF and MaterialDesign\n\n" +
                              "Features:\n" +
                              "• Generate Excel files from project templates\n" +
                              "• Upload Excel data to Azure SQL database\n" +
                              "• Data validation and error checking\n" +
                              "• Modern Material Design UI\n\n" +
                              $"Log file: {_loggingService.GetLogFilePath()}";

            MessageBox.Show(aboutMessage, "About Data Capture Benchmark", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void MenuUserGuide_Click(object sender, RoutedEventArgs e)
        {
            var userGuide = "Data Capture Benchmark - User Guide\n\n" +
                           "GENERATE EXCEL:\n" +
                           "1. Type in the search box to find projects\n" +
                           "2. Select a project from the search results\n" +
                           "3. Click 'Generate Excel File' to create a new file\n" +
                           "4. The file will be saved to your Desktop\n\n" +
                           "UPLOAD DATA:\n" +
                           "1. Click 'Browse' to select an Excel file\n" +
                           "2. Enter your database connection string\n" +
                           "3. Click 'Test Connection' to verify database access\n" +
                           "4. Review validation results\n" +
                           "5. Click 'Upload Data' to insert data into the database\n\n" +
                           "TIPS:\n" +
                           "• Use the menu to save your connection string\n" +
                           "• Check logs if you encounter any issues\n" +
                           "• Ensure Excel files have the required columns: Source, Discipline, Attribute, Value, Confidence";

            MessageBox.Show(userGuide, "User Guide", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        #endregion
    }
}