﻿<Window x:Class="DataCapture_Benchmark.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:DataCapture_Benchmark"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        mc:Ignorable="d"
        Title="Data Capture Benchmark" Height="700" Width="1000"
        TextElement.Foreground="{DynamicResource MaterialDesignBody}"
        TextElement.FontWeight="Regular"
        TextElement.FontSize="13"
        TextOptions.TextFormattingMode="Ideal"
        TextOptions.TextRenderingMode="Auto"
        Background="{DynamicResource MaterialDesignPaper}"
        FontFamily="{DynamicResource MaterialDesignFont}"
        WindowStartupLocation="CenterScreen">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Menu Bar -->
        <Menu Grid.Row="0" Background="{DynamicResource MaterialDesignPaper}">
            <MenuItem Header="_File">
                <MenuItem Header="_Open Excel File..." Click="MenuOpenExcel_Click"/>
                <MenuItem Header="_Save Configuration" Click="MenuSaveConfig_Click"/>
                <Separator/>
                <MenuItem Header="E_xit" Click="MenuExit_Click"/>
            </MenuItem>
            <MenuItem Header="_Tools">
                <MenuItem Header="_View Logs" Click="MenuViewLogs_Click"/>
                <MenuItem Header="_Clear Logs" Click="MenuClearLogs_Click"/>
                <Separator/>
                <MenuItem Header="_Test Database Connection" Click="MenuTestConnection_Click"/>
            </MenuItem>
            <MenuItem Header="_Help">
                <MenuItem Header="_About" Click="MenuAbout_Click"/>
                <MenuItem Header="_User Guide" Click="MenuUserGuide_Click"/>
            </MenuItem>
        </Menu>

        <!-- Header -->
        <materialDesign:ColorZone Grid.Row="1" Mode="PrimaryMid" Padding="16">
            <DockPanel>
                <materialDesign:PackIcon Kind="FileExcel" Width="32" Height="32" VerticalAlignment="Center" DockPanel.Dock="Left"/>
                <TextBlock Text="Data Capture Benchmark" FontSize="22" FontWeight="Medium" VerticalAlignment="Center" Margin="16,0,0,0"/>
            </DockPanel>
        </materialDesign:ColorZone>

        <!-- Main Content -->
        <TabControl Grid.Row="2" Style="{StaticResource MaterialDesignTabControl}" Margin="16">
            <TabItem Header="Generate Excel">
                <materialDesign:Card Margin="16" Padding="16">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="*"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>

                        <TextBlock Grid.Row="0" Text="Generate Excel File" Style="{StaticResource MaterialDesignHeadline5TextBlock}" Margin="0,0,0,16"/>

                        <TextBox Grid.Row="1" x:Name="SearchTextBox"
                                materialDesign:HintAssist.Hint="Search projects..."
                                Style="{StaticResource MaterialDesignFloatingHintTextBox}"
                                Margin="0,0,0,16"
                                TextChanged="SearchTextBox_TextChanged"/>

                        <ListBox Grid.Row="2" x:Name="ProjectListBox"
                                Height="200"
                                Style="{StaticResource MaterialDesignListBox}"
                                SelectionChanged="ProjectListBox_SelectionChanged"
                                Margin="0,0,0,16"/>

                        <StackPanel Grid.Row="3" x:Name="SelectedProjectPanel" Visibility="Collapsed" Margin="0,0,0,16">
                            <TextBlock Text="Selected Project:" Style="{StaticResource MaterialDesignSubtitle1TextBlock}" Margin="0,0,0,8"/>
                            <materialDesign:Card Padding="16" Background="{DynamicResource MaterialDesignSelection}">
                                <StackPanel>
                                    <TextBlock x:Name="SelectedProjectName" FontWeight="Medium"/>
                                    <TextBlock x:Name="SelectedProjectDetails" Opacity="0.7" TextWrapping="Wrap"/>
                                </StackPanel>
                            </materialDesign:Card>
                        </StackPanel>

                        <Button Grid.Row="4" x:Name="GenerateButton"
                                Content="Generate Excel File"
                                Style="{StaticResource MaterialDesignRaisedButton}"
                                IsEnabled="False"
                                Click="GenerateButton_Click"
                                HorizontalAlignment="Left"/>
                    </Grid>
                </materialDesign:Card>
            </TabItem>

            <TabItem Header="Upload Data">
                <materialDesign:Card Margin="16" Padding="16">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="*"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>

                        <TextBlock Grid.Row="0" Text="Upload Data to Database" Style="{StaticResource MaterialDesignHeadline5TextBlock}" Margin="0,0,0,16"/>

                        <DockPanel Grid.Row="1" Margin="0,0,0,16">
                            <Button DockPanel.Dock="Right" x:Name="BrowseButton"
                                    Content="Browse"
                                    Style="{StaticResource MaterialDesignOutlinedButton}"
                                    Click="BrowseButton_Click"
                                    Margin="8,0,0,0"/>
                            <TextBox x:Name="FilePathTextBox"
                                    materialDesign:HintAssist.Hint="Excel file path..."
                                    Style="{StaticResource MaterialDesignFloatingHintTextBox}"
                                    IsReadOnly="True"/>
                        </DockPanel>

                        <TextBox Grid.Row="2" x:Name="ConnectionStringTextBox"
                                materialDesign:HintAssist.Hint="Database connection string..."
                                Style="{StaticResource MaterialDesignFloatingHintTextBox}"
                                Margin="0,0,0,16"/>

                        <Button Grid.Row="3" x:Name="TestConnectionButton"
                                Content="Test Connection"
                                Style="{StaticResource MaterialDesignOutlinedButton}"
                                Click="TestConnectionButton_Click"
                                HorizontalAlignment="Left"
                                Margin="0,0,0,16"/>

                        <ScrollViewer Grid.Row="4" VerticalScrollBarVisibility="Auto" Margin="0,0,0,16">
                            <TextBlock x:Name="ValidationResultsTextBlock"
                                      TextWrapping="Wrap"
                                      FontFamily="Consolas"
                                      Background="{DynamicResource MaterialDesignTextFieldBoxBackground}"
                                      Padding="8"/>
                        </ScrollViewer>

                        <Button Grid.Row="5" x:Name="UploadButton"
                                Content="Upload Data"
                                Style="{StaticResource MaterialDesignRaisedButton}"
                                IsEnabled="False"
                                Click="UploadButton_Click"
                                HorizontalAlignment="Left"/>
                    </Grid>
                </materialDesign:Card>
            </TabItem>
        </TabControl>

        <!-- Status Bar -->
        <materialDesign:ColorZone Grid.Row="3" Mode="PrimaryLight" Padding="8">
            <DockPanel>
                <Button DockPanel.Dock="Right" x:Name="ViewLogsButton"
                        Content="View Logs"
                        Style="{StaticResource MaterialDesignFlatButton}"
                        Click="MenuViewLogs_Click"
                        Margin="8,0,0,0"/>
                <TextBlock x:Name="StatusTextBlock" Text="Ready" VerticalAlignment="Center"/>
            </DockPanel>
        </materialDesign:ColorZone>
    </Grid>
</Window>
