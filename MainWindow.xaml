﻿<Window x:Class="DataCapture_Benchmark.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:DataCapture_Benchmark"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        mc:Ignorable="d"
        Title="Data Capture Benchmark" Height="700" Width="1000"
        WindowStartupLocation="CenterScreen"
        Background="White">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Menu Bar -->
        <Menu Grid.Row="0" Background="{DynamicResource MaterialDesignPaper}">
            <MenuItem Header="_File">
                <MenuItem Header="_Open Excel File..." Click="MenuOpenExcel_Click"/>
                <MenuItem Header="_Save Configuration" Click="MenuSaveConfig_Click"/>
                <Separator/>
                <MenuItem Header="E_xit" Click="MenuExit_Click"/>
            </MenuItem>
            <MenuItem Header="_Tools">
                <MenuItem Header="_View Logs" Click="MenuViewLogs_Click"/>
                <MenuItem Header="_Clear Logs" Click="MenuClearLogs_Click"/>
                <Separator/>
                <MenuItem Header="_Test Database Connection" Click="MenuTestConnection_Click"/>
            </MenuItem>
            <MenuItem Header="_Help">
                <MenuItem Header="_About" Click="MenuAbout_Click"/>
                <MenuItem Header="_User Guide" Click="MenuUserGuide_Click"/>
            </MenuItem>
        </Menu>

        <!-- Header -->
        <Border Grid.Row="1" Background="DarkBlue" Padding="16">
            <DockPanel>
                <TextBlock Text="📊" FontSize="32" VerticalAlignment="Center" DockPanel.Dock="Left" Foreground="White"/>
                <TextBlock Text="Data Capture Benchmark" FontSize="22" FontWeight="Medium" VerticalAlignment="Center" Margin="16,0,0,0" Foreground="White"/>
            </DockPanel>
        </Border>

        <!-- Main Content -->
        <TabControl Grid.Row="2" Margin="16">
            <TabItem Header="Generate Excel">
                <Border Margin="16" Padding="16" Background="White" BorderBrush="LightGray" BorderThickness="1">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="*"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>

                        <TextBlock Grid.Row="0" Text="Generate Excel File" FontSize="20" FontWeight="Medium" Margin="0,0,0,16"/>

                        <TextBox Grid.Row="1" x:Name="SearchTextBox"
                                Text=""
                                Margin="0,0,0,16"
                                TextChanged="SearchTextBox_TextChanged">
                            <TextBox.Style>
                                <Style TargetType="TextBox">
                                    <Setter Property="Padding" Value="8"/>
                                    <Setter Property="BorderThickness" Value="1"/>
                                    <Setter Property="BorderBrush" Value="Gray"/>
                                </Style>
                            </TextBox.Style>
                        </TextBox>
                        <TextBlock Grid.Row="1" Text="Search projects..." Foreground="Gray" Margin="12,8,0,0" IsHitTestVisible="False">
                            <TextBlock.Style>
                                <Style TargetType="TextBlock">
                                    <Setter Property="Visibility" Value="Collapsed"/>
                                    <Style.Triggers>
                                        <DataTrigger Binding="{Binding Text, ElementName=SearchTextBox}" Value="">
                                            <Setter Property="Visibility" Value="Visible"/>
                                        </DataTrigger>
                                    </Style.Triggers>
                                </Style>
                            </TextBlock.Style>
                        </TextBlock>

                        <ListBox Grid.Row="2" x:Name="ProjectListBox"
                                Height="200"
                                SelectionChanged="ProjectListBox_SelectionChanged"
                                Margin="0,0,0,16"/>

                        <StackPanel Grid.Row="3" x:Name="SelectedProjectPanel" Visibility="Collapsed" Margin="0,0,0,16">
                            <TextBlock Text="Selected Project:" FontSize="14" FontWeight="Medium" Margin="0,0,0,8"/>
                            <Border Padding="16" Background="LightBlue" BorderBrush="Gray" BorderThickness="1">
                                <StackPanel>
                                    <TextBlock x:Name="SelectedProjectName" FontWeight="Medium"/>
                                    <TextBlock x:Name="SelectedProjectDetails" Foreground="DarkGray" TextWrapping="Wrap"/>
                                </StackPanel>
                            </Border>
                        </StackPanel>

                        <Button Grid.Row="4" x:Name="GenerateButton"
                                Content="Generate Excel File"
                                IsEnabled="False"
                                Click="GenerateButton_Click"
                                HorizontalAlignment="Left"
                                Padding="12,8"
                                Background="DarkBlue"
                                Foreground="White"
                                BorderThickness="0"/>
                    </Grid>
                </Border>
            </TabItem>

            <TabItem Header="Upload Data">
                <Border Margin="16" Padding="16" Background="White" BorderBrush="LightGray" BorderThickness="1">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="*"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>

                        <TextBlock Grid.Row="0" Text="Upload Data to Database" FontSize="20" FontWeight="Medium" Margin="0,0,0,16"/>

                        <DockPanel Grid.Row="1" Margin="0,0,0,16">
                            <Button DockPanel.Dock="Right" x:Name="BrowseButton"
                                    Content="Browse"
                                    Click="BrowseButton_Click"
                                    Margin="8,0,0,0"
                                    Padding="8,4"
                                    Background="LightGray"
                                    BorderThickness="1"/>
                            <TextBox x:Name="FilePathTextBox"
                                    IsReadOnly="True"
                                    Padding="8"
                                    BorderThickness="1"
                                    BorderBrush="Gray"/>
                        </DockPanel>
                        <TextBlock Grid.Row="1" Text="Excel file path..." Foreground="Gray" Margin="12,8,0,0" IsHitTestVisible="False">
                            <TextBlock.Style>
                                <Style TargetType="TextBlock">
                                    <Setter Property="Visibility" Value="Collapsed"/>
                                    <Style.Triggers>
                                        <DataTrigger Binding="{Binding Text, ElementName=FilePathTextBox}" Value="">
                                            <Setter Property="Visibility" Value="Visible"/>
                                        </DataTrigger>
                                    </Style.Triggers>
                                </Style>
                            </TextBlock.Style>
                        </TextBlock>

                        <TextBox Grid.Row="2" x:Name="ConnectionStringTextBox"
                                Margin="0,0,0,16"
                                Padding="8"
                                BorderThickness="1"
                                BorderBrush="Gray"/>
                        <TextBlock Grid.Row="2" Text="Database connection string..." Foreground="Gray" Margin="12,8,0,0" IsHitTestVisible="False">
                            <TextBlock.Style>
                                <Style TargetType="TextBlock">
                                    <Setter Property="Visibility" Value="Collapsed"/>
                                    <Style.Triggers>
                                        <DataTrigger Binding="{Binding Text, ElementName=ConnectionStringTextBox}" Value="">
                                            <Setter Property="Visibility" Value="Visible"/>
                                        </DataTrigger>
                                    </Style.Triggers>
                                </Style>
                            </TextBlock.Style>
                        </TextBlock>

                        <Button Grid.Row="3" x:Name="TestConnectionButton"
                                Content="Test Connection"
                                Click="TestConnectionButton_Click"
                                HorizontalAlignment="Left"
                                Margin="0,0,0,16"
                                Padding="8,4"
                                Background="LightGray"
                                BorderThickness="1"/>

                        <ScrollViewer Grid.Row="4" VerticalScrollBarVisibility="Auto" Margin="0,0,0,16">
                            <TextBlock x:Name="ValidationResultsTextBlock"
                                      TextWrapping="Wrap"
                                      FontFamily="Consolas"
                                      Background="LightGray"
                                      Padding="8"/>
                        </ScrollViewer>

                        <Button Grid.Row="5" x:Name="UploadButton"
                                Content="Upload Data"
                                IsEnabled="False"
                                Click="UploadButton_Click"
                                HorizontalAlignment="Left"
                                Padding="12,8"
                                Background="DarkBlue"
                                Foreground="White"
                                BorderThickness="0"/>
                    </Grid>
                </Border>
            </TabItem>
        </TabControl>

        <!-- Status Bar -->
        <Border Grid.Row="3" Background="LightBlue" Padding="8" BorderBrush="Gray" BorderThickness="0,1,0,0">
            <DockPanel>
                <Button DockPanel.Dock="Right" x:Name="ViewLogsButton"
                        Content="View Logs"
                        Click="MenuViewLogs_Click"
                        Margin="8,0,0,0"
                        Padding="6,2"
                        Background="Transparent"
                        BorderThickness="1"/>
                <TextBlock x:Name="StatusTextBlock" Text="Ready" VerticalAlignment="Center"/>
            </DockPanel>
        </Border>
    </Grid>
</Window>
