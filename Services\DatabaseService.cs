using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Microsoft.Data.SqlClient;
using DataCapture_Benchmark.Models;

namespace DataCapture_Benchmark.Services
{
    public class DatabaseService
    {
        private readonly string _connectionString;

        public DatabaseService(string connectionString)
        {
            _connectionString = connectionString;
        }

        public async Task<int> InsertTransactionAsync(string username, DateTime date, string filename)
        {
            const string query = @"
                INSERT INTO BM_Transaction ([Username], [Date], [StartTime], [Filename])
                OUTPUT INSERTED.TxId
                VALUES (@Username, @Date, @StartTime, @Filename)";

            try
            {
                using var connection = new SqlConnection(_connectionString);
                using var command = new SqlCommand(query, connection);
                
                command.Parameters.AddWithValue("@Username", username);
                command.Parameters.AddWithValue("@Date", date.Date);
                command.Parameters.AddWithValue("@StartTime", date.TimeOfDay);
                command.Parameters.AddWithValue("@Filename", filename);

                await connection.OpenAsync();
                var result = await command.ExecuteScalarAsync();

                return result != null ? (int)result : 0;
            }
            catch (Exception ex)
            {
                throw new Exception($"Error inserting transaction: {ex.Message}", ex);
            }
        }

        public async Task InsertBMValueAsync(int txId, ExcelRowData rowData)
        {
            const string query = @"
                INSERT INTO BM_Value 
                    ([TxId], [Source], [Discipline], [Attribute], [Value], [Confidence], [Project_ID], [Project_Number])
                VALUES 
                    (@TxId, @Source, @Discipline, @Attribute, @Value, @Confidence, @Project_ID, @Project_Number)";

            try
            {
                using var connection = new SqlConnection(_connectionString);
                using var command = new SqlCommand(query, connection);
                
                command.Parameters.AddWithValue("@TxId", txId);
                command.Parameters.AddWithValue("@Source", rowData.Source);
                command.Parameters.AddWithValue("@Discipline", rowData.Discipline);
                command.Parameters.AddWithValue("@Attribute", rowData.Attribute);
                command.Parameters.AddWithValue("@Value", string.IsNullOrEmpty(rowData.Value) ? DBNull.Value : rowData.Value);
                command.Parameters.AddWithValue("@Confidence", rowData.Confidence.HasValue ? rowData.Confidence.Value : DBNull.Value);
                command.Parameters.AddWithValue("@Project_ID", rowData.ProjectId);
                command.Parameters.AddWithValue("@Project_Number", rowData.ProjectNumber);

                await connection.OpenAsync();
                await command.ExecuteNonQueryAsync();
            }
            catch (Exception ex)
            {
                throw new Exception($"Error inserting BM_Value: {ex.Message}", ex);
            }
        }

        public async Task<bool> TestConnectionAsync()
        {
            try
            {
                using var connection = new SqlConnection(_connectionString);
                await connection.OpenAsync();
                return true;
            }
            catch
            {
                return false;
            }
        }

        public async Task UploadDataAsync(List<ExcelRowData> data, string filename, Guid projectId, int projectNumber)
        {
            if (data == null || data.Count == 0)
            {
                throw new ArgumentException("No data to upload");
            }

            try
            {
                // Set project info for all rows
                foreach (var row in data)
                {
                    row.ProjectId = projectId;
                    row.ProjectNumber = projectNumber;
                }

                // Insert transaction record
                var username = Environment.UserName;
                var txId = await InsertTransactionAsync(username, DateTime.Now, filename);

                // Insert all BM_Value records
                foreach (var row in data)
                {
                    await InsertBMValueAsync(txId, row);
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"Error uploading data: {ex.Message}", ex);
            }
        }

        public List<string> ValidateData(List<ExcelRowData> data)
        {
            var errors = new List<string>();

            if (data == null || data.Count == 0)
            {
                errors.Add("No data found to validate");
                return errors;
            }

            for (int i = 0; i < data.Count; i++)
            {
                var row = data[i];
                var rowNumber = i + 2; // +2 because Excel is 1-based and we skip header

                // Required field validations
                if (string.IsNullOrWhiteSpace(row.Source))
                    errors.Add($"Row {rowNumber}: Source is required");
                else if (row.Source.Length > 50)
                    errors.Add($"Row {rowNumber}: Source must be 50 characters or less");

                if (string.IsNullOrWhiteSpace(row.Discipline))
                    errors.Add($"Row {rowNumber}: Discipline is required");
                else if (row.Discipline.Length > 50)
                    errors.Add($"Row {rowNumber}: Discipline must be 50 characters or less");

                if (string.IsNullOrWhiteSpace(row.Attribute))
                    errors.Add($"Row {rowNumber}: Attribute is required");
                else if (row.Attribute.Length > 150)
                    errors.Add($"Row {rowNumber}: Attribute must be 150 characters or less");

                // Optional field validations
                if (!string.IsNullOrEmpty(row.Value) && row.Value.Length > 255)
                    errors.Add($"Row {rowNumber}: Value must be 255 characters or less");

                if (row.Confidence.HasValue && (row.Confidence < 0 || row.Confidence > 100))
                    errors.Add($"Row {rowNumber}: Confidence must be between 0 and 100");

                // These will be set during upload, but validate if already set
                if (row.ProjectId != Guid.Empty && row.ProjectId == Guid.Empty)
                    errors.Add($"Row {rowNumber}: Invalid Project ID format");

                if (row.ProjectNumber < 0)
                    errors.Add($"Row {rowNumber}: Project Number cannot be negative");
            }

            return errors;
        }

        public async Task<List<string>> ValidateDataAsync(List<ExcelRowData> data, Guid projectId, int projectNumber)
        {
            var errors = ValidateData(data);

            // Set project info for validation
            foreach (var row in data)
            {
                row.ProjectId = projectId;
                row.ProjectNumber = projectNumber;
            }

            // Additional database-specific validations could go here
            // For example, checking if Source/Discipline combinations exist in reference tables
            await Task.CompletedTask; // Placeholder for future async validation

            return errors;
        }
    }
}
