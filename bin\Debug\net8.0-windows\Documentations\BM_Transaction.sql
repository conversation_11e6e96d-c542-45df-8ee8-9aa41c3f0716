CREATE TABLE [dbo].[BM_Transaction](
	[Username] [varchar](255) NULL,
	[Date] [date] NULL,
	[StartTime] [time](7) NULL,
	[TxId]  AS (abs(checksum(([Username]+CONVERT([varchar],[Date],(120)))+CONVERT([varchar],[StartTime],(114))))) PERSISTED NOT NULL,
	[Filename] [varchar](255) NULL,
PRIMARY KEY CLUSTERED 
(
	[TxId] ASC
)WITH (STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO