using System;

namespace DataCapture_Benchmark.Models
{
    public class ProjectInfo
    {
        public int ProjectNumber { get; set; }
        public Guid PUID { get; set; }
        public int Priority { get; set; }
        public string Name { get; set; } = string.Empty;
        public string Building { get; set; } = string.Empty;
        public string Client { get; set; } = string.Empty;
        public string JobDirector { get; set; } = string.Empty;
        public string JobManager { get; set; } = string.Empty;
        public string ClientMarketSegment { get; set; } = string.Empty;

        public string DisplayText => $"{Name} - {Building} ({ProjectNumber})";
    }
}
