# Data Capture Benchmark Application

A modern WPF desktop application for generating Excel files and uploading data to Azure SQL databases, built with MaterialDesign UI.

## Features

### 🔧 Generate Excel
- Search and select projects from ProjectList.csv
- Generate Excel files based on predefined templates
- Automatically populate Job Director information
- Save files with timestamp and project PUID naming

### 📊 Upload Data
- Read Excel files with data validation
- Upload data to Azure SQL database tables (BM_Transaction and BM_Value)
- Comprehensive data validation and error reporting
- Support for batch operations up to 2000 rows

### 🎨 Modern UI
- Material Design interface with modern styling
- Intuitive tabbed navigation
- Real-time status updates and progress feedback
- Comprehensive error handling and user guidance

## Requirements

- Windows 10 or later
- .NET 8.0 Runtime
- Excel (for opening generated files)
- Azure SQL Database or SQL Server access

## Installation

1. Clone or download the repository
2. Open the solution in Visual Studio 2022 or later
3. Restore NuGet packages
4. Build the solution
5. Run the application

## Configuration

### Database Connection
The application supports multiple connection string formats:
- Windows Authentication: `Server=your_server;Database=your_database;Trusted_Connection=true;TrustServerCertificate=true;`
- Azure SQL: `Server=tcp:your_server.database.windows.net,1433;Initial Catalog=your_database;User ID=your_username;Password=your_password;Encrypt=True;`

### Project Data
Ensure the `Documentations/ProjectList.csv` file contains your project data with the following columns:
- ProjectNumber
- PUID
- Priority
- Name
- Building
- Client
- JobDirector
- JobManager
- ClientMarketSegment

### Excel Template
The `Documentations/ExcelTemplateToGenerate.xlsx` file serves as the template for generated Excel files.

## Usage

### Generating Excel Files
1. Open the application
2. Navigate to the "Generate Excel" tab
3. Type in the search box to find projects
4. Select a project from the search results
5. Click "Generate Excel File"
6. The file will be saved to your Desktop with a timestamp

### Uploading Data
1. Navigate to the "Upload Data" tab
2. Click "Browse" to select an Excel file
3. Enter your database connection string
4. Click "Test Connection" to verify database access
5. Review validation results
6. Select the target project for the data
7. Click "Upload Data" to insert into the database

## Database Schema

### BM_Transaction Table
- Username (varchar(255))
- Date (date)
- StartTime (time)
- TxId (computed, primary key)
- Filename (varchar(255))

### BM_Value Table
- TxId (int, foreign key)
- Source (varchar(50), required)
- Discipline (varchar(50), required)
- Attribute (varchar(150), required)
- Value (varchar(255))
- Confidence (decimal(10,2))
- Project_ID (uniqueidentifier, required)
- Project_Number (int, required)

## Excel File Format

Expected columns in Excel files for upload:
- **Source**: Data source identifier (required, max 50 chars)
- **Discipline**: Engineering discipline (required, max 50 chars)
- **Attribute**: Attribute name (required, max 150 chars)
- **Value**: Attribute value (optional, max 255 chars)
- **Confidence**: Confidence percentage 0-100 (optional)

## Troubleshooting

### Common Issues

1. **"Projects not loading"**
   - Ensure `Documentations/ProjectList.csv` exists and is properly formatted
   - Check the application logs for detailed error information

2. **"Database connection failed"**
   - Verify your connection string format
   - Ensure network connectivity to the database server
   - Check firewall settings and SQL Server configuration

3. **"Excel file validation errors"**
   - Ensure the Excel file has the required column headers
   - Check for empty required fields (Source, Discipline, Attribute)
   - Verify data types and length constraints

### Logging
- Application logs are automatically created in the `Logs` folder
- Use the "View Logs" menu option to open the current log file
- Logs include detailed error information and operation history

## Development

### Technologies Used
- WPF (.NET 8.0)
- MaterialDesignThemes for UI
- ClosedXML for Excel operations
- Microsoft.Data.SqlClient for database connectivity
- CsvHelper for CSV file processing

### Project Structure
```
DataCapture_Benchmark/
├── Models/              # Data models
├── Services/            # Business logic services
├── Views/               # Additional UI components
├── Utilities/           # Helper utilities
├── Documentations/      # Reference files and templates
└── Logs/               # Application logs (created at runtime)
```

## License

This project is for internal use and follows company guidelines for data handling and security.

## Support

For technical support or feature requests, please contact the development team or check the application logs for detailed error information.
