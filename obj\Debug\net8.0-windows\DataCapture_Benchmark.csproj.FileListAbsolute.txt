C:\Users\<USER>\source\repos\DataCapture_Benchmark\DataCapture_Benchmark\bin\Debug\net8.0-windows\DataCapture_Benchmark.exe
C:\Users\<USER>\source\repos\DataCapture_Benchmark\DataCapture_Benchmark\bin\Debug\net8.0-windows\DataCapture_Benchmark.deps.json
C:\Users\<USER>\source\repos\DataCapture_Benchmark\DataCapture_Benchmark\bin\Debug\net8.0-windows\DataCapture_Benchmark.runtimeconfig.json
C:\Users\<USER>\source\repos\DataCapture_Benchmark\DataCapture_Benchmark\bin\Debug\net8.0-windows\DataCapture_Benchmark.dll
C:\Users\<USER>\source\repos\DataCapture_Benchmark\DataCapture_Benchmark\bin\Debug\net8.0-windows\DataCapture_Benchmark.pdb
C:\Users\<USER>\source\repos\DataCapture_Benchmark\DataCapture_Benchmark\bin\Debug\net8.0-windows\MaterialDesignColors.dll
C:\Users\<USER>\source\repos\DataCapture_Benchmark\DataCapture_Benchmark\bin\Debug\net8.0-windows\MaterialDesignThemes.Wpf.dll
C:\Users\<USER>\source\repos\DataCapture_Benchmark\DataCapture_Benchmark\bin\Debug\net8.0-windows\Microsoft.Xaml.Behaviors.dll
C:\Users\<USER>\source\repos\DataCapture_Benchmark\DataCapture_Benchmark\obj\Debug\net8.0-windows\DataCapture_Benchmark.csproj.AssemblyReference.cache
C:\Users\<USER>\source\repos\DataCapture_Benchmark\DataCapture_Benchmark\obj\Debug\net8.0-windows\MainWindow.g.cs
C:\Users\<USER>\source\repos\DataCapture_Benchmark\DataCapture_Benchmark\obj\Debug\net8.0-windows\App.g.cs
C:\Users\<USER>\source\repos\DataCapture_Benchmark\DataCapture_Benchmark\obj\Debug\net8.0-windows\DataCapture_Benchmark_MarkupCompile.cache
C:\Users\<USER>\source\repos\DataCapture_Benchmark\DataCapture_Benchmark\obj\Debug\net8.0-windows\DataCapture_Benchmark_MarkupCompile.lref
C:\Users\<USER>\source\repos\DataCapture_Benchmark\DataCapture_Benchmark\obj\Debug\net8.0-windows\MainWindow.baml
C:\Users\<USER>\source\repos\DataCapture_Benchmark\DataCapture_Benchmark\obj\Debug\net8.0-windows\DataCapture_Benchmark.g.resources
C:\Users\<USER>\source\repos\DataCapture_Benchmark\DataCapture_Benchmark\obj\Debug\net8.0-windows\DataCapture_Benchmark.GeneratedMSBuildEditorConfig.editorconfig
C:\Users\<USER>\source\repos\DataCapture_Benchmark\DataCapture_Benchmark\obj\Debug\net8.0-windows\DataCapture_Benchmark.AssemblyInfoInputs.cache
C:\Users\<USER>\source\repos\DataCapture_Benchmark\DataCapture_Benchmark\obj\Debug\net8.0-windows\DataCapture_Benchmark.AssemblyInfo.cs
C:\Users\<USER>\source\repos\DataCapture_Benchmark\DataCapture_Benchmark\obj\Debug\net8.0-windows\DataCapture_Benchmark.csproj.CoreCompileInputs.cache
C:\Users\<USER>\source\repos\DataCapture_Benchmark\DataCapture_Benchmark\obj\Debug\net8.0-windows\DataCapt.6799B3D2.Up2Date
C:\Users\<USER>\source\repos\DataCapture_Benchmark\DataCapture_Benchmark\obj\Debug\net8.0-windows\DataCapture_Benchmark.dll
C:\Users\<USER>\source\repos\DataCapture_Benchmark\DataCapture_Benchmark\obj\Debug\net8.0-windows\refint\DataCapture_Benchmark.dll
C:\Users\<USER>\source\repos\DataCapture_Benchmark\DataCapture_Benchmark\obj\Debug\net8.0-windows\DataCapture_Benchmark.pdb
C:\Users\<USER>\source\repos\DataCapture_Benchmark\DataCapture_Benchmark\obj\Debug\net8.0-windows\DataCapture_Benchmark.genruntimeconfig.cache
C:\Users\<USER>\source\repos\DataCapture_Benchmark\DataCapture_Benchmark\obj\Debug\net8.0-windows\ref\DataCapture_Benchmark.dll
