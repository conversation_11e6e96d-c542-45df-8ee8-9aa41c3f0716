<Window x:Class="DataCapture_Benchmark.ProjectSelectionDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="Select Project" Height="500" Width="600"
        WindowStartupLocation="CenterOwner"
        Background="White">
    
    <Grid Margin="16">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <TextBlock Grid.Row="0" Text="Select Project for Data Upload"
                   FontSize="18" FontWeight="Medium"
                   Margin="0,0,0,16"/>

        <Grid Grid.Row="1">
            <TextBox x:Name="SearchTextBox"
                    Margin="0,0,0,16"
                    Padding="8"
                    BorderThickness="1"
                    BorderBrush="Gray"
                    TextChanged="SearchTextBox_TextChanged"/>
            <TextBlock Text="Search projects..."
                      Foreground="Gray"
                      Margin="12,8,0,0"
                      IsHitTestVisible="False">
                <TextBlock.Style>
                    <Style TargetType="TextBlock">
                        <Setter Property="Visibility" Value="Collapsed"/>
                        <Style.Triggers>
                            <DataTrigger Binding="{Binding Text, ElementName=SearchTextBox}" Value="">
                                <Setter Property="Visibility" Value="Visible"/>
                            </DataTrigger>
                        </Style.Triggers>
                    </Style>
                </TextBlock.Style>
            </TextBlock>
        </Grid>
        
        <ListBox Grid.Row="2" x:Name="ProjectListBox"
                SelectionChanged="ProjectListBox_SelectionChanged"
                BorderThickness="1"
                BorderBrush="Gray">
            <ListBox.ItemTemplate>
                <DataTemplate>
                    <Border Padding="8" BorderThickness="0,0,0,1" BorderBrush="LightGray">
                        <StackPanel>
                            <TextBlock Text="{Binding Name}" FontWeight="Medium" FontSize="14"/>
                            <TextBlock Text="{Binding Building}" Foreground="DarkGray" FontSize="12"/>
                            <TextBlock Text="{Binding Client}" Foreground="DarkGray" FontSize="12"/>
                            <TextBlock Text="{Binding ProjectNumber, StringFormat='Project: {0}'}" Foreground="Gray" FontSize="10"/>
                        </StackPanel>
                    </Border>
                </DataTemplate>
            </ListBox.ItemTemplate>
        </ListBox>
        
        <StackPanel Grid.Row="3" Orientation="Horizontal" HorizontalAlignment="Right" Margin="0,16,0,0">
            <Button x:Name="CancelButton" Content="Cancel"
                    Click="CancelButton_Click"
                    Margin="0,0,8,0"
                    Padding="12,6"
                    Background="LightGray"
                    BorderThickness="1"/>
            <Button x:Name="OkButton" Content="OK"
                    IsEnabled="False"
                    Click="OkButton_Click"
                    Padding="12,6"
                    Background="DarkBlue"
                    Foreground="White"
                    BorderThickness="0"/>
        </StackPanel>
    </Grid>
</Window>
