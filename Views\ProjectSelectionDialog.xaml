<Window x:Class="DataCapture_Benchmark.ProjectSelectionDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="Select Project" Height="500" Width="600"
        WindowStartupLocation="CenterOwner"
        TextElement.Foreground="{DynamicResource MaterialDesignBody}"
        TextElement.FontWeight="Regular"
        TextElement.FontSize="13"
        TextOptions.TextFormattingMode="Ideal"
        TextOptions.TextRenderingMode="Auto"
        Background="{DynamicResource MaterialDesignPaper}"
        FontFamily="{DynamicResource MaterialDesignFont}">
    
    <Grid Margin="16">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <TextBox Grid.Row="1" x:Name="SearchTextBox" 
                materialDesign:HintAssist.Hint="Search projects..." 
                Margin="0,0,0,16"
                TextChanged="SearchTextBox_TextChanged"/>
        
        <ListBox Grid.Row="2" x:Name="ProjectListBox" 
                SelectionChanged="ProjectListBox_SelectionChanged">
            <ListBox.ItemTemplate>
                <DataTemplate>
                    <StackPanel Margin="8">
                        <TextBlock Text="{Binding Name}" FontWeight="Medium"/>
                        <TextBlock Text="{Binding Building}" Opacity="0.7"/>
                        <TextBlock Text="{Binding Client}" Opacity="0.7"/>
                        <TextBlock Text="{Binding ProjectNumber, StringFormat='Project: {0}'}" Opacity="0.5" FontSize="11"/>
                    </StackPanel>
                </DataTemplate>
            </ListBox.ItemTemplate>
        </ListBox>
        
        <StackPanel Grid.Row="3" Orientation="Horizontal" HorizontalAlignment="Right" Margin="0,16,0,0">
            <Button x:Name="CancelButton" Content="Cancel" 
                    Click="CancelButton_Click"
                    Margin="0,0,8,0"/>
            <Button x:Name="OkButton" Content="OK" 
                    IsEnabled="False"
                    Click="OkButton_Click"/>
        </StackPanel>
    </Grid>
</Window>
