using System;
using Microsoft.Data.SqlClient;

class Program
{
    static void Main(string[] args)
    {
        string connectionString = "Your_Connection_String";

        // Insert BM_Transaction record and retrieve TxId
        int txId = InsertTransaction(GetConnection(connectionString), "JohnDoe", DateTime.Now, "example.xlsx");

        // Insert BM_Value record using the generated TxId
        InsertBMValue(GetConnection(connectionString), txId, "SourceName", "Electrical", "PowerUsage", "150KW", 98.5m, Guid.NewGuid(), 101);

        Console.WriteLine("Data insertion complete.");
    }

    private static SqlConnection GetConnection(string connectionString)
    {
        return new SqlConnection(connectionString);
    }

    private static int InsertTransaction(SqlConnection connection, string username, DateTime date, string filename)
    {
        string query = @"
            INSERT INTO BM_Transaction ([Username], [Date], [StartTime], [Filename])
            OUTPUT INSERTED.TxId
            VALUES (@Username, @Date, @StartTime, @Filename)";
        
        int txId;

        using (var command = new SqlCommand(query, connection))
        {
            connection.Open();
            command.Parameters.AddWithValue("@Username", username);
            command.Parameters.AddWithValue("@Date", date.Date);
            command.Parameters.AddWithValue("@StartTime", date.TimeOfDay);
            command.Parameters.AddWithValue("@Filename", filename);

            txId = (int)command.ExecuteScalar();
            connection.Close();
        }

        Console.WriteLine($"Generated TxId: {txId}");
        return txId;
    }

    private static void InsertBMValue(SqlConnection connection, int txId, string source, string discipline, string attribute, string value, decimal confidence, Guid projectId, int projectNumber)
    {
        string query = @"
            INSERT INTO BM_Value 
                ([TxId], [Source], [Discipline], [Attribute], [Value], [Confidence], [Project_ID], [Project_Number])
            VALUES 
                (@TxId, @Source, @Discipline, @Attribute, @Value, @Confidence, @Project_ID, @Project_Number)";
        
        using (var command = new SqlCommand(query, connection))
        {
            connection.Open();
            command.Parameters.AddWithValue("@TxId", txId);
            command.Parameters.AddWithValue("@Source", source);
            command.Parameters.AddWithValue("@Discipline", discipline);
            command.Parameters.AddWithValue("@Attribute", attribute);
            command.Parameters.AddWithValue("@Value", value ?? (object)DBNull.Value);
            command.Parameters.AddWithValue("@Confidence", confidence);
            command.Parameters.AddWithValue("@Project_ID", projectId);
            command.Parameters.AddWithValue("@Project_Number", projectNumber);

            command.ExecuteNonQuery();
            connection.Close();
        }
    }
}