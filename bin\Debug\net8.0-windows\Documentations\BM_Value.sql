=CREATE TABLE [dbo].[BM_Value](
	[TxId] [int] NULL,
	[Source] [varchar](50) NOT NULL,
	[Discipline] [varchar](50) NOT NULL,
	[Attribute] [varchar](150) NOT NULL,
	[Value] [varchar](255) NULL,
	[Confidence] [decimal](10, 2) NULL,
	[Project_ID] [uniqueidentifier] NOT NULL,
	[Project_Number] [int] NOT NULL,
 CONSTRAINT [PK_PUID_Source_Attribute] PRIMARY KEY CLUSTERED 
(
	[Project_ID] ASC,
	[Source] ASC,
	[Attribute] ASC
)WITH (STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO

ALTER TABLE [dbo].[BM_Value]  WITH CHECK ADD FOREIGN KEY([Discipline], [Attribute])
REFERENCES [dbo].[BM_Attribute] ([Discipline], [Attribute])
ON UPDATE CASCADE
GO

ALTER TABLE [dbo].[BM_Value]  WITH CHECK ADD FOREIGN KEY([Source])
REFERENCES [dbo].[BM_Source] ([Name])
ON UPDATE CASCADE
GO

ALTER TABLE [dbo].[BM_Value]  WITH CHECK ADD FOREIGN KEY([TxId])
REFERENCES [dbo].[BM_Transaction] ([TxId])
ON UPDATE CASCADE
ON DELETE CASCADE
GO