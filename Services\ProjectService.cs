using System;
using System.Collections.Generic;
using System.Globalization;
using System.IO;
using System.Linq;
using CsvHelper;
using DataCapture_Benchmark.Models;

namespace DataCapture_Benchmark.Services
{
    public class ProjectService
    {
        private List<ProjectInfo> _projects = new();

        public void LoadProjects()
        {
            try
            {
                var csvPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Documentations", "ProjectList.csv");
                
                if (!File.Exists(csvPath))
                {
                    throw new FileNotFoundException($"ProjectList.csv not found at: {csvPath}");
                }

                using var reader = new StringReader(File.ReadAllText(csvPath));
                using var csv = new CsvReader(reader, CultureInfo.InvariantCulture);
                
                csv.Read();
                csv.ReadHeader();
                
                _projects.Clear();
                
                while (csv.Read())
                {
                    var project = new ProjectInfo
                    {
                        ProjectNumber = csv.GetField<int>("ProjectNumber"),
                        PUID = csv.GetField<Guid>("PUID"),
                        Priority = csv.GetField<int>("Priority"),
                        Name = csv.GetField<string>("Name") ?? string.Empty,
                        Building = csv.GetField<string>("Building") ?? string.Empty,
                        Client = csv.GetField<string>("Client") ?? string.Empty,
                        JobDirector = csv.GetField<string>("JobDirector") ?? string.Empty,
                        JobManager = csv.GetField<string>("JobManager") ?? string.Empty,
                        ClientMarketSegment = csv.GetField<string>("ClientMarketSegment") ?? string.Empty
                    };
                    
                    _projects.Add(project);
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"Error loading projects: {ex.Message}", ex);
            }
        }

        public List<ProjectInfo> SearchProjects(string searchTerm, int maxResults = 8)
        {
            if (string.IsNullOrWhiteSpace(searchTerm))
                return new List<ProjectInfo>();

            var searchTermLower = searchTerm.ToLower();
            
            return _projects
                .Where(p => 
                    p.Name.ToLower().Contains(searchTermLower) ||
                    p.Building.ToLower().Contains(searchTermLower) ||
                    p.Client.ToLower().Contains(searchTermLower) ||
                    p.ProjectNumber.ToString().Contains(searchTermLower))
                .Take(maxResults)
                .ToList();
        }

        public ProjectInfo? GetProjectByPUID(Guid puid)
        {
            return _projects.FirstOrDefault(p => p.PUID == puid);
        }
    }
}
