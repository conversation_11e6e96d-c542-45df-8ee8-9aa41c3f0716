using System;
using System.IO;
using ClosedXML.Excel;

namespace DataCapture_Benchmark.Utilities
{
    public static class ExcelTemplateAnalyzer
    {
        public static void AnalyzeTemplate()
        {
            try
            {
                var templatePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Documentations", "ExcelTemplateToGenerate.xlsx");
                
                if (!File.Exists(templatePath))
                {
                    Console.WriteLine($"Template not found at: {templatePath}");
                    return;
                }

                using var workbook = new XLWorkbook(templatePath);
                var worksheet = workbook.Worksheets.First();
                
                Console.WriteLine("Excel Template Analysis:");
                Console.WriteLine($"Worksheet Name: {worksheet.Name}");
                Console.WriteLine($"Used Range: {worksheet.RangeUsed()?.RangeAddress}");
                
                var usedRange = worksheet.RangeUsed();
                if (usedRange != null)
                {
                    Console.WriteLine("\nFirst 10 rows of data:");
                    var rowCount = Math.Min(10, usedRange.RowCount());
                    var colCount = Math.Min(10, usedRange.ColumnCount());
                    
                    for (int row = 1; row <= rowCount; row++)
                    {
                        Console.Write($"Row {row}: ");
                        for (int col = 1; col <= colCount; col++)
                        {
                            var cellValue = worksheet.Cell(row, col).GetString();
                            Console.Write($"[{cellValue}] ");
                        }
                        Console.WriteLine();
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error analyzing template: {ex.Message}");
            }
        }
    }
}
