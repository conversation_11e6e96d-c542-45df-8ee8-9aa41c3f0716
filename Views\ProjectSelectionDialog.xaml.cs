using System.Windows;
using System.Windows.Controls;
using DataCapture_Benchmark.Models;
using DataCapture_Benchmark.Services;

namespace DataCapture_Benchmark
{
    public partial class ProjectSelectionDialog : Window
    {
        private readonly ProjectService _projectService;
        public ProjectInfo? SelectedProject { get; private set; }

        public ProjectSelectionDialog(ProjectService projectService)
        {
            InitializeComponent();
            _projectService = projectService;
        }

        private void SearchTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            var searchTerm = SearchTextBox.Text;
            
            if (string.IsNullOrWhiteSpace(searchTerm))
            {
                ProjectListBox.ItemsSource = null;
                return;
            }

            var results = _projectService.SearchProjects(searchTerm, 20); // Show more results in dialog
            ProjectListBox.ItemsSource = results;
        }

        private void ProjectListBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            SelectedProject = ProjectListBox.SelectedItem as ProjectInfo;
            OkButton.IsEnabled = SelectedProject != null;
        }

        private void OkButton_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = true;
            Close();
        }

        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }
    }
}
