using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using DataCapture_Benchmark.Models;
using DataCapture_Benchmark.Services;

namespace DataCapture_Benchmark
{
    public partial class ProjectSelectionDialog : Window
    {
        private readonly ProjectService _projectService;
        private List<ProjectInfo> _allProjects = new();
        public ProjectInfo? SelectedProject { get; private set; }

        public ProjectSelectionDialog(ProjectService projectService)
        {
            InitializeComponent();
            _projectService = projectService;
            LoadAllProjects();
        }

        private void LoadAllProjects()
        {
            // Get all projects without any search filter
            _allProjects = _projectService.SearchProjects("", int.MaxValue);

            // If no search term, show all projects
            if (_allProjects.Count == 0)
            {
                // Try to get projects with a broad search to get all
                _allProjects = _projectService.GetAllProjects();
            }

            ProjectListBox.ItemsSource = _allProjects;
        }

        private void SearchTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            var searchTerm = SearchTextBox.Text?.Trim() ?? string.Empty;

            if (string.IsNullOrWhiteSpace(searchTerm))
            {
                // Show all projects when search is empty
                ProjectListBox.ItemsSource = _allProjects;
                return;
            }

            // Filter projects based on search term
            var filteredProjects = _allProjects.Where(p =>
                p.Name.Contains(searchTerm, StringComparison.OrdinalIgnoreCase) ||
                p.Building.Contains(searchTerm, StringComparison.OrdinalIgnoreCase) ||
                p.Client.Contains(searchTerm, StringComparison.OrdinalIgnoreCase) ||
                p.ProjectNumber.ToString().Contains(searchTerm) ||
                p.JobDirector.Contains(searchTerm, StringComparison.OrdinalIgnoreCase) ||
                p.JobManager.Contains(searchTerm, StringComparison.OrdinalIgnoreCase)
            ).ToList();

            ProjectListBox.ItemsSource = filteredProjects;
        }

        private void ProjectListBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            SelectedProject = ProjectListBox.SelectedItem as ProjectInfo;
            OkButton.IsEnabled = SelectedProject != null;
        }

        private void OkButton_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = true;
            Close();
        }

        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }
    }
}
