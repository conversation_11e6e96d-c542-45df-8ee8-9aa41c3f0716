using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using ClosedXML.Excel;
using DataCapture_Benchmark.Models;

namespace DataCapture_Benchmark.Services
{
    public class ExcelService
    {
        public string GenerateExcelFile(ProjectInfo selectedProject)
        {
            try
            {
                var templatePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Documentations", "ExcelTemplateToGenerate.xlsx");

                if (!File.Exists(templatePath))
                {
                    throw new FileNotFoundException($"Excel template not found at: {templatePath}");
                }

                // Create output filename
                var timestamp = DateTime.Now.ToString("yyyyMMdd_HHmmss");
                var outputFileName = $"{timestamp}_DataEntry_{selectedProject.PUID}.xlsx";
                var outputPath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.Desktop), outputFileName);

                // Copy template to output location
                File.Copy(templatePath, outputPath, true);

                // Open and modify the copied file
                using var workbook = new XLWorkbook(outputPath);
                var worksheet = workbook.Worksheets.First();

                // Find and update the JOB DIRECTOR value
                UpdateJobDirectorValue(worksheet, selectedProject.JobDirector);

                // Add project metadata to the worksheet
                AddProjectMetadata(worksheet, selectedProject);

                workbook.Save();

                return outputPath;
            }
            catch (Exception ex)
            {
                throw new Exception($"Error generating Excel file: {ex.Message}", ex);
            }
        }

        private void UpdateJobDirectorValue(IXLWorksheet worksheet, string jobDirector)
        {
            var usedRange = worksheet.RangeUsed();
            if (usedRange == null) return;

            // Search for "JOB DIRECTOR" in all cells and update the adjacent Value cell
            foreach (var row in usedRange.Rows())
            {
                foreach (var cell in row.Cells())
                {
                    if (cell.GetString().Equals("JOB DIRECTOR", StringComparison.OrdinalIgnoreCase))
                    {
                        // Try different patterns for finding the value cell
                        // Pattern 1: Value in next column
                        var valueCell = cell.CellRight();
                        if (valueCell != null && string.IsNullOrEmpty(valueCell.GetString()))
                        {
                            valueCell.Value = jobDirector;
                            return;
                        }

                        // Pattern 2: Value in same row, different column (common in forms)
                        // Look for "Value" header or empty cell in common value columns
                        var valueCellInRow = row.Cells().FirstOrDefault(c =>
                            c.Address.ColumnNumber > cell.Address.ColumnNumber &&
                            (string.IsNullOrEmpty(c.GetString()) || c.GetString().Equals("Value", StringComparison.OrdinalIgnoreCase)));

                        if (valueCellInRow != null)
                        {
                            valueCellInRow.Value = jobDirector;
                            return;
                        }
                    }
                }
            }
        }

        private void AddProjectMetadata(IXLWorksheet worksheet, ProjectInfo project)
        {
            // Add project information to a metadata section (usually at the top or in a separate area)
            // This is a common pattern in Excel templates

            // Find a good place to add metadata (look for empty area or specific metadata section)
            var metadataStartRow = 1;
            var metadataStartCol = 1;

            // Try to find if there's already a metadata section
            var usedRange = worksheet.RangeUsed();
            if (usedRange != null)
            {
                // Look for existing metadata or use area after used range
                metadataStartCol = Math.Max(1, usedRange.LastColumn().ColumnNumber() + 2);
            }

            // Add project metadata
            worksheet.Cell(metadataStartRow, metadataStartCol).Value = "Project Metadata";
            worksheet.Cell(metadataStartRow, metadataStartCol).Style.Font.Bold = true;

            worksheet.Cell(metadataStartRow + 1, metadataStartCol).Value = "Project Number:";
            worksheet.Cell(metadataStartRow + 1, metadataStartCol + 1).Value = project.ProjectNumber;

            worksheet.Cell(metadataStartRow + 2, metadataStartCol).Value = "PUID:";
            worksheet.Cell(metadataStartRow + 2, metadataStartCol + 1).Value = project.PUID.ToString();

            worksheet.Cell(metadataStartRow + 3, metadataStartCol).Value = "Project Name:";
            worksheet.Cell(metadataStartRow + 3, metadataStartCol + 1).Value = project.Name;

            worksheet.Cell(metadataStartRow + 4, metadataStartCol).Value = "Building:";
            worksheet.Cell(metadataStartRow + 4, metadataStartCol + 1).Value = project.Building;

            worksheet.Cell(metadataStartRow + 5, metadataStartCol).Value = "Client:";
            worksheet.Cell(metadataStartRow + 5, metadataStartCol + 1).Value = project.Client;

            worksheet.Cell(metadataStartRow + 6, metadataStartCol).Value = "Job Director:";
            worksheet.Cell(metadataStartRow + 6, metadataStartCol + 1).Value = project.JobDirector;

            worksheet.Cell(metadataStartRow + 7, metadataStartCol).Value = "Job Manager:";
            worksheet.Cell(metadataStartRow + 7, metadataStartCol + 1).Value = project.JobManager;
        }

        public void OpenExcelFile(string filePath)
        {
            try
            {
                if (!File.Exists(filePath))
                {
                    throw new FileNotFoundException($"Excel file not found: {filePath}");
                }

                Process.Start(new ProcessStartInfo
                {
                    FileName = filePath,
                    UseShellExecute = true
                });
            }
            catch (Exception ex)
            {
                throw new Exception($"Error opening Excel file: {ex.Message}", ex);
            }
        }

        public List<ExcelRowData> ReadExcelFile(string filePath)
        {
            try
            {
                if (!File.Exists(filePath))
                {
                    throw new FileNotFoundException($"Excel file not found: {filePath}");
                }

                var data = new List<ExcelRowData>();

                using var workbook = new XLWorkbook(filePath);
                var worksheet = workbook.Worksheets.First();
                var usedRange = worksheet.RangeUsed();

                if (usedRange == null)
                {
                    return data; // Empty worksheet
                }

                // Try to find the header row and data columns
                var headerRow = FindHeaderRow(worksheet);
                var columnMapping = GetColumnMapping(worksheet, headerRow);

                if (headerRow == 0 || columnMapping.Count == 0)
                {
                    throw new Exception("Could not find valid data structure in Excel file. Expected columns: Source, Discipline, Attribute, Value, Confidence");
                }

                // Read data rows
                var dataRows = usedRange.Rows().Skip(headerRow); // Skip header and any rows before it

                foreach (var row in dataRows)
                {
                    var rowData = new ExcelRowData();
                    bool hasData = false;

                    if (columnMapping.ContainsKey("Source"))
                    {
                        rowData.Source = row.Cell(columnMapping["Source"]).GetString();
                        hasData = hasData || !string.IsNullOrWhiteSpace(rowData.Source);
                    }

                    if (columnMapping.ContainsKey("Discipline"))
                    {
                        rowData.Discipline = row.Cell(columnMapping["Discipline"]).GetString();
                        hasData = hasData || !string.IsNullOrWhiteSpace(rowData.Discipline);
                    }

                    if (columnMapping.ContainsKey("Attribute"))
                    {
                        rowData.Attribute = row.Cell(columnMapping["Attribute"]).GetString();
                        hasData = hasData || !string.IsNullOrWhiteSpace(rowData.Attribute);
                    }

                    if (columnMapping.ContainsKey("Value"))
                    {
                        rowData.Value = row.Cell(columnMapping["Value"]).GetString();
                    }

                    if (columnMapping.ContainsKey("Confidence"))
                    {
                        if (row.Cell(columnMapping["Confidence"]).TryGetValue(out decimal confidence))
                        {
                            rowData.Confidence = confidence;
                        }
                    }

                    // Only add rows that have at least some data
                    if (hasData && !string.IsNullOrWhiteSpace(rowData.Attribute))
                    {
                        data.Add(rowData);
                    }
                }

                return data;
            }
            catch (Exception ex)
            {
                throw new Exception($"Error reading Excel file: {ex.Message}", ex);
            }
        }

        private int FindHeaderRow(IXLWorksheet worksheet)
        {
            var usedRange = worksheet.RangeUsed();
            if (usedRange == null) return 0;

            // Look for a row that contains expected column headers
            var expectedHeaders = new[] { "Source", "Discipline", "Attribute", "Value", "Confidence" };

            for (int rowNum = 1; rowNum <= Math.Min(10, usedRange.RowCount()); rowNum++)
            {
                var row = worksheet.Row(rowNum);
                var cellValues = row.Cells(1, Math.Min(10, usedRange.ColumnCount()))
                                   .Select(c => c.GetString().Trim())
                                   .ToList();

                // Check if this row contains at least 3 of our expected headers
                var matchCount = expectedHeaders.Count(header =>
                    cellValues.Any(cell => cell.Equals(header, StringComparison.OrdinalIgnoreCase)));

                if (matchCount >= 3)
                {
                    return rowNum;
                }
            }

            // If no header row found, assume first row is header
            return 1;
        }

        private Dictionary<string, int> GetColumnMapping(IXLWorksheet worksheet, int headerRow)
        {
            var mapping = new Dictionary<string, int>();
            var expectedHeaders = new[] { "Source", "Discipline", "Attribute", "Value", "Confidence" };

            if (headerRow == 0) return mapping;

            var row = worksheet.Row(headerRow);
            var usedRange = worksheet.RangeUsed();
            if (usedRange == null) return mapping;

            for (int col = 1; col <= usedRange.ColumnCount(); col++)
            {
                var cellValue = row.Cell(col).GetString().Trim();

                foreach (var expectedHeader in expectedHeaders)
                {
                    if (cellValue.Equals(expectedHeader, StringComparison.OrdinalIgnoreCase))
                    {
                        mapping[expectedHeader] = col;
                        break;
                    }
                }
            }

            return mapping;
        }
    }
}
