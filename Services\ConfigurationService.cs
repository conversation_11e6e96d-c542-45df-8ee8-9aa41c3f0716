using System;
using System.IO;
using System.Text.Json;

namespace DataCapture_Benchmark.Services
{
    public class ConfigurationService
    {
        private readonly string _configPath;
        private AppConfiguration? _configuration;

        public ConfigurationService()
        {
            _configPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "appsettings.json");
        }

        public AppConfiguration GetConfiguration()
        {
            if (_configuration == null)
            {
                LoadConfiguration();
            }
            return _configuration ?? new AppConfiguration();
        }

        private void LoadConfiguration()
        {
            try
            {
                if (File.Exists(_configPath))
                {
                    var json = File.ReadAllText(_configPath);
                    _configuration = JsonSerializer.Deserialize<AppConfiguration>(json, new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });
                }
                else
                {
                    _configuration = new AppConfiguration();
                }
            }
            catch (Exception)
            {
                _configuration = new AppConfiguration();
            }
        }

        public void SaveConfiguration(AppConfiguration configuration)
        {
            try
            {
                var json = JsonSerializer.Serialize(configuration, new JsonSerializerOptions
                {
                    WriteIndented = true
                });
                File.WriteAllText(_configPath, json);
                _configuration = configuration;
            }
            catch (Exception ex)
            {
                throw new Exception($"Error saving configuration: {ex.Message}", ex);
            }
        }
    }

    public class AppConfiguration
    {
        public ConnectionStrings ConnectionStrings { get; set; } = new();
        public AppSettings AppSettings { get; set; } = new();
    }

    public class ConnectionStrings
    {
        public string DefaultConnection { get; set; } = string.Empty;
        public string AzureSqlConnection { get; set; } = string.Empty;
    }

    public class AppSettings
    {
        public int MaxSearchResults { get; set; } = 8;
        public int MaxUploadRows { get; set; } = 2000;
        public string DefaultOutputFolder { get; set; } = "Desktop";
    }
}
