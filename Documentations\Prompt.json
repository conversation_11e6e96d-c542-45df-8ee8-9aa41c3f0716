{"project_name": "WPF Desktop Application: Generate Excel and Upload Data", "description": "A desktop application with two main features: 'Generate Excel' and 'Upload Data'. The application allows users to read existing Excel data, generate a new Excel file with predefined schemas, manually edit it, and upload the data to Azure SQL tables ([BM_Transaction] and [BM_Value]).", "features": [{"name": "Generate Excel", "description": "Reads data from an existing Excel file and generates a new Excel file based on user-selected data. The generated file will have predefined columns and formatting.", "functional_requirements": ["Read from ProjectList.csv inthe Documentations folder", "Add a search bar to filter the rows from ProjectList.csv, show 8 closest match like in Google Map Address search, user will select the correct row", "Copy from ExcelTemplateToGenerate.xlsx in the Documentations folder, save the file with name: '{DateTime.Now()}_DataEntry_{PUID}}.xlsx'", "Fill the Value column from row with Attribute column value 'JOB DIRECTOR' with the value from the selected row'JOB DIRECTOR' column in ProjectList.csv", "Open the generated (copied) excel file locally for user editing."], "technical_requirements": ["Use ClosedXML ."]}, {"name": "Upload Data", "description": "Reads a saved Excel file and uploads its data to Azure SQL tables. Handles data validation and inserts rows into BM_Transaction (to generate TxId) and BM_Value.", "functional_requirements": ["Allow users to specify the path to the saved Excel file.", "Refer to BM_Transaction.sql and BM_Value.sql for the schema of the two tables.", "Validate required fields (e.g., Project_ID, Attribute) before insertion.", "Insert data into BM_Transaction table: Auto-generate TxId using Username, Date, and StartTime.", "Insert data into BM_Value table: Use the same generated TxId from BM_Transaction, Project_ID from PUID, and Project_Number. Map columns from Excel file to table fields.", "DataInsertionReferenceCode.cs in the Documentations folder can be used as reference.", "Provide error messages for invalid data formats or missing fields."], "technical_requirements": ["Use parameterized SQL queries to avoid SQL injection.", "Establish a secure connection to Azure SQL database using ADO.NET or Entity Framework.", "Implement validation for GUIDs, decimals, integers, and other expected types."]}], "non_functional_requirements": {"usability": "The application should have an intuitive UI with clear instructions for each task.", "UI Design": "Implement Modern UI design with MaterialDesignThemes.", "performance": "Generate and upload Excel files efficiently, supporting up to 2000 rows per file.", "security": "Encrypt Azure SQL database connections using TLS.", "compatibility": "Ensure compatibility with Windows 10 and above. Support .xls and .xlsx file formats.", "reliability": "Handle edge cases such as missing fields and format mismatches gracefully, displaying meaningful error messages."}, "risks": [{"risk": "Invalid Excel File", "description": "Users may attempt to upload files with missing or corrupted data.", "mitigation": "Validate the file structure and display detailed error messages for corrections."}, {"risk": "Data Format Issues", "description": "Fields like PUID, TxId, or decimals might not conform to expected formats during data upload.", "mitigation": "Implement strict validation checks before inserting data into the database."}, {"risk": "Performance Bottlenecks", "description": "Handling large datasets in Excel files may slow down the application.", "mitigation": "Optimize file reading/writing with libraries like ClosedXML and use efficient batch operations for database updates."}]}