[2025-08-12 14:59:42] [INFO] Application starting
[2025-08-12 14:59:58] [ERROR] Error loading projects
Exception: System.Exception: Error loading projects: The conversion cannot be performed.
    Text: 'NO'
    MemberName: 
    MemberType: 
    TypeConverter: 'CsvHelper.TypeConversion.Int32Converter'
IReader state:
   ColumnCount: 0
   CurrentIndex: 2
   HeaderRecord:
["ProjectNumber","PUID","Priority","Name","Building","Client","JobDirector","JobManager","ClientMarketSegment"]
IParser state:
   ByteCount: 0
   CharCount: 16842
   Row: 99
   RawRow: 99
   Count: 9
   RawRecord:
3258521,2A1EDB46-4B25-AE1E-4282-BE73D6C1B186,NO,Wellington SMF Project,Main Process Building,Wellington City Council,Wayne Estment,<PERSON>,Local Government


 ---> CsvHelper.TypeConversion.TypeConverterException: The conversion cannot be performed.
    Text: 'NO'
    MemberName: 
    MemberType: 
    TypeConverter: 'CsvHelper.TypeConversion.Int32Converter'
IReader state:
   ColumnCount: 0
   CurrentIndex: 2
   HeaderRecord:
["ProjectNumber","PUID","Priority","Name","Building","Client","JobDirector","JobManager","ClientMarketSegment"]
IParser state:
   ByteCount: 0
   CharCount: 16842
   Row: 99
   RawRow: 99
   Count: 9
   RawRecord:
3258521,2A1EDB46-4B25-AE1E-4282-BE73D6C1B186,NO,Wellington SMF Project,Main Process Building,Wellington City Council,Wayne Estment,Daniel Maier,Local Government


   at CsvHelper.TypeConversion.DefaultTypeConverter.ConvertFromString(String text, IReaderRow row, MemberMapData memberMapData)
   at CsvHelper.TypeConversion.Int32Converter.ConvertFromString(String text, IReaderRow row, MemberMapData memberMapData)
   at CsvHelper.CsvReader.GetField(Type type, Int32 index, ITypeConverter converter)
   at CsvHelper.CsvReader.GetField[T](Int32 index, ITypeConverter converter)
   at CsvHelper.CsvReader.GetField[T](String name, ITypeConverter converter)
   at CsvHelper.CsvReader.GetField[T](String name)
   at DataCapture_Benchmark.Services.ProjectService.LoadProjects() in C:\Users\<USER>\source\repos\DataCapture_Benchmark\DataCapture_Benchmark\Services\ProjectService.cs:line 36
   --- End of inner exception stack trace ---
   at DataCapture_Benchmark.Services.ProjectService.LoadProjects() in C:\Users\<USER>\source\repos\DataCapture_Benchmark\DataCapture_Benchmark\Services\ProjectService.cs:line 54
   at DataCapture_Benchmark.MainWindow.<LoadProjects>b__9_0() in C:\Users\<USER>\source\repos\DataCapture_Benchmark\DataCapture_Benchmark\MainWindow.xaml.cs:line 69
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at DataCapture_Benchmark.MainWindow.LoadProjects() in C:\Users\<USER>\source\repos\DataCapture_Benchmark\DataCapture_Benchmark\MainWindow.xaml.cs:line 69
[2025-08-12 15:00:00] [INFO] Application initialized successfully
[2025-08-12 15:04:00] [INFO] Application starting
[2025-08-12 15:09:50] [INFO] Application starting
[2025-08-12 15:09:51] [INFO] Projects loaded successfully
[2025-08-12 15:09:51] [INFO] Application initialized successfully
[2025-08-12 15:20:09] [INFO] Application starting
[2025-08-12 15:20:10] [INFO] Projects loaded successfully
[2025-08-12 15:20:10] [INFO] Application initialized successfully
[2025-08-12 15:21:16] [INFO] Application starting
[2025-08-12 15:21:17] [INFO] Projects loaded successfully
[2025-08-12 15:21:17] [INFO] Application initialized successfully
[2025-08-12 15:22:55] [INFO] Application starting
[2025-08-12 15:22:55] [INFO] Projects loaded successfully
[2025-08-12 15:22:55] [INFO] Application initialized successfully
